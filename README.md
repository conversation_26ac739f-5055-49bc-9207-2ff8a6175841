<h1 align="center">
  <a href="https://jaaz.app" target="_blank"> Jaaz.app</a>
  <p align="center">Open source Canva AI alternative</p>

 <p align="center">
  <a href="https://jaaz.app">
    <img src="https://github.com/user-attachments/assets/e0cffb94-8c6f-4867-800a-c144aceb6d54" alt="Jaaz Logo" />
  </a>
</p>

</h2>
<p align="center">The world's first open-source multimodal creative assistant</p>
<p align="center">This is a substitute for <PERSON><PERSON> and <PERSON><PERSON> that prioritizes privacy and is usable locally.</p>
<br><br>

<p align="center">
    <a href="https://github.com/11cafe/jaaz/blob/main/README_zh.md">中文版</a>|
  <a href="https://mxnpt25l6k.feishu.cn/docx/LvcTdlVbFoRAZWxnhBYcqVydnpc">新手指南</a>
  </p>


<p align="center">
 <a href="https://discord.gg/https://discord.gg/SMRe5n3m">
  <img src="https://img.shields.io/badge/Discord-5865F2?logo=discord&logoColor=white&style=for-the-badge" alt="Discord" />
  </a>
   <a href="https://github.com/11cafe/jaaz/stargazers">
    <img src="https://img.shields.io/github/stars/11cafe/jaaz?style=for-the-badge&logo=github" alt="GitHub Stars" />
     </a>  
<!-- Download for Mac -->
  <a href="https://jaaz.app/api/downloads/mac-latest">
    <img src="https://img.shields.io/badge/For%20Mac-000000?logo=apple&logoColor=white&style=for-the-badge" alt="Download for Mac" />
  </a>

  <!-- Download for Windows -->
  <a href="https://jaaz.app/api/downloads/windows-latest">
    <img src="https://img.shields.io/badge/For%20Windows-0078D6?logo=laptop&logoColor=white&style=for-the-badge" alt="Download for Windows" />
  </a>
</p>
<p align="center">
Magic Canva!
  
"Build" your ideas like playing with LEGO—paint directly, point with arrows, and the AI instantly understands and generates results.
<img width="900" alt="Screenshot 2025-06-02 at 3 03 49 PM" src="https://github.com/user-attachments/assets/543b170c-14f7-4a73-96bd-909662138592" />
<img width="900" alt="Screenshot 2025-06-02 at 3 03 49 PM" src="https://github.com/user-attachments/assets/7dd9af32-cc60-4145-9b30-7db96d8fa09a" />


Magic video!

https://github.com/user-attachments/assets/b7abf987-c65d-49b1-8178-82770873c583


Create Viral Shorts with a Single Sentence
<video src="https://github.com/user-attachments/assets/1c15e792-098a-4557-b310-d9c223f73442" controls width="100%" />






## ✨ Getting started & staying tuned with us.

Star us, and you will receive all release notifications from GitHub without any delay!
<img width="900" alt="Screenshot 2025-06-02 at 3 03 49 PM" src="https://github.com/user-attachments/assets/1c9a3661-80a4-4fba-a30f-f469898b0aec" />

## ✨ Key Features

🎬 One-Prompt Image & Video Generation
Turn one prompt into complete images or videos in seconds.

 -Supports GPT-4o, Midjourney, VEO3, Kling,veo3,seedance etc.

 -Auto-optimized prompts & multi-turn refinement

🧙 Magic Canvas&Magic Video
Prompt-free creation — build like Lego.

 -Simple sketching and free combination — AI instantly understands and generates.

 -AI understands and generates instantly

 -No prompt writing needed
 
 -Describe steps simply on the video, and AI will generate following them.

🖼️ Infinite Canvas & Visual Storyboarding
Plan scenes with an unlimited canvas

 -Link layouts, manage media visually

 -Real-time collaboration supported

🤖 Smart AI Agent System
 -Chat to insert objects, transfer styles, control logic

 -Works with local (ComfyUI) & cloud models

 -Maintains multi-character coherence

⚙️ Flexible Deployment & Local Assets
 -Fully offline or hybrid setup (Ollama + APIs)

 -Built-in library for media & prompts

 -Cross-platform: Windows & macOS

🔐 Privacy & Security
 -Local-first, no data leaves your device

 -Open-source, no tracking

 -Safe for commercial use — you own your data

---

## Usage
Download here: https://jaaz.app/

Click the "Log In" button at the top right of the homepage to access API models. With a low-cost plan, you can seamlessly use a variety of powerful APIs.

<img width="400" alt="Screenshot 2025-06-02 at 3 08 51 PM" src="https://github.com/user-attachments/assets/0055557d-c247-4801-ac3f-01ed4fa775ae" />


Start chatting with agent to generate stories or storyboards!



## Cases
<img width="889" height="1103" alt="Frame 122" src="https://github.com/user-attachments/assets/90503110-0f5c-4297-bbfe-6d35e3f54d4c" />

- Prompt: Help me place this character in six different scenes, all in front of landmark buildings from around the world. The lighting is harmonious. He takes photos from all over the world, realistic, with warm light, high picture quality, and a picture ratio of 9:16

![814c563b08f6ef44de0c2c31f0fdd00b-min](https://github.com/user-attachments/assets/4e2634b3-9068-47cd-a18f-ddde8f218d25)

<img width="1000" alt="Screenshot 2025-06-02 at 3 51 56 AM" src="https://github.com/user-attachments/assets/5d8efe74-99b0-41bc-aa3e-6f7b92b69c36" />


<img width="900" alt="Screenshot 2025-06-02 at 3 51 56 AM" src="https://github.com/user-attachments/assets/186982a9-5e4e-4ac1-a42c-c840092fd616" />

<img width="900" alt="Screenshot 2025-06-02 at 3 03 49 PM" src="https://github.com/user-attachments/assets/b8508efd-def8-40ed-8ab5-62ed3c26de67" />

![image26](https://github.com/user-attachments/assets/2065cabd-af32-43b6-bc01-59a935d9a287)

## Team and Enterprise Support:
Support for multi-user private deployment of enterprise teams, ensuring privacy and security.

Please contact via email: <EMAIL>

WeChat: aifox1


## Manual Install (For Linux or local builds)

🟠 **Need Python version >=3.12**

First git clone this repo:

`git clone https://github.com/11cafe/localart`

`cd react`

`npm install --force`

`npx vite build`

`cd ../server`

`pip install -r requirements.txt`

`python main.py`

## Development

🟠 **Need Python version >=3.12**

VSCode/Cursor Install Extensions：

- Black Formatter by ms-python (ms-python.black-formatter)

`cd react`

`npm install --force && npm run dev`

`cd server`

`pip install -r requirements.txt`

`python main.py`








