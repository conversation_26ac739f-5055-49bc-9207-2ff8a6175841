{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noEmitOnError": true, "typeRoots": ["./node_modules/@types", "../node_modules/@types"], "types": ["node", "electron"]}, "include": ["*.ts", "**/*.ts"], "exclude": ["node_modules", "dist", "test"]}