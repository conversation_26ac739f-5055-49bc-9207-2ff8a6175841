Jaaz 许可协议 (Jaaz Licensing Policy) 
English Version
This project is dual-licensed under the Jaaz Community License and a Commercial License.
Core Principle:
Jaaz Community License: This free license is for individuals and limited use within organizations.
Individuals can use all Jaaz functions, and the content they generate may be used for commercial projects.
Companies/Teams can use the software's standard, out-of-the-box functions.
Commercial License: Required for any internal team deployment, or for any secondary development or modification of the source code.
1. Jaaz Community License
Permitted Use for Individuals:
Individuals are granted a free, perpetual right to use all functions of the Jaaz software.
Any content, designs, or assets generated by an individual using the software may be used for personal or commercial purposes.
Permitted Use for Organizations (Companies & Teams):
Organizations are permitted to use the standard, unmodified functions of the software as provided.
Strict Prohibitions: Under the Jaaz Community License, you are strictly prohibited from the following without a Commercial License:
Internal Team Deployment: Installing, hosting, or operating the software in a manner that allows access by multiple users within an organization simultaneously (e.g., on a shared server or internal cloud).
Code Modification & Secondary Development: Modifying, translating, reverse-engineering, decompiling, or creating derivative works based on the Jaaz source code.
Redistribution: Redistributing the software, whether modified or unmodified, as part of another product or service.

2. Commercial License
Mandatory Requirement: You MUST purchase a commercial license for any of the following activities:
Internal multi-user or team deployment.
Any secondary development or modification of the Jaaz source code.
Embedding or redistributing Jaaz as part of your own commercial offering.
Needing technical support, warranties, or indemnification.
A commercial license provides the legal right to deploy Jaaz for internal teams and to modify its source code for your organization's specific needs.
To Obtain a Commercial License: Please contact the Jaaz team by <NAME_EMAIL> to discuss commercial licensing.

3. Contributions
We welcome community contributions. By submitting a contribution, you agree to grant the project maintainers a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable license to use, reproduce, and sublicense your contribution under any license, including the Commercial License.

4. Additional Terms
Intellectual Property & UI: The visual and interaction design of Jaaz is protected under applicable UI/UX design rights and may not be copied or resold as a derivative product without permission.
© 2025 Jaaz Contributors & 11cafe Studio. All rights reserved.

中文版本 (Chinese Version) 
本项目采用 Jaaz 社区许可证 和 商业许可证 的双重许可模式。

核心原则：
Jaaz 社区许可证： 面向个人及组织有限使用的免费许可证。
个人： 可以使用Jaaz所有功能，生成的内容可用于商业项目。
公司/团队： 可以使用软件提供的标准、开箱即用的功能。
商业许可证： 任何内部团队部署，或对代码的任何二次开发或修改，都必须获取商业许可证。
1. Jaaz 社区许可证 (Jaaz Community License)
个人用户授权用途：
个人用户被授予权利来使用 Jaaz 软件的功能。
个人用户使用本软件生成的任何内容、设计或资产，均可用于个人或商业目的。
组织（公司&团队）授权用途：
组织被允许使用本软件按原样提供的、未经修改的标准功能。
严格禁止用途： 若无商业许可证，Jaaz 社区许可证严格禁止以下行为：
内部团队部署： 以允许多个组织内部用户同时访问的方式安装、托管或操作本软件（例如：部署在共享服务器或内部云上）。
代码修改与二次开发： 修改、翻译、逆向工程、反编译或基于 Jaaz 源代码创造衍生作品。
再分发： 将本软件（无论修改与否）作为另一产品或服务的一部分进行再分发。
2. 商业许可证 (Commercial License)

强制要求： 进行以下任何活动，您都必须购买商业许可证：

进行内部多用户或团队部署。

对 Jaaz 源代码进行任何二次开发或修改。

将 Jaaz 作为您自己商业产品的一部分进行嵌入或再分发。

需要技术支持、保证或赔偿。

商业许可证为您提供了为内部团队部署 Jaaz 以及根据您组织的特定需求修改其源代码的合法权利。

获取商业许可： 请通过邮箱 <EMAIL> 联系 Jaaz 开发团队洽谈商业授权事宜。

3. 贡献 (Contributions)
我们欢迎社区贡献。通过提交贡献，您同意授予项目维护者一项永久的、全球性的、非排他的、免费的、免版税的、不可撤销的许可，允许其在任何许可（包括商业许可）下使用、复制和再许可您的贡献。
4. 其他条款 (Additional Terms)
知识产权与用户界面 (Intellectual Property & UI)： Jaaz 的视觉与交互设计受适用的 UI/UX 设计权保护，未经许可，不得抄袭或作为衍生产品转售。

© 2025 Jaaz 贡献者 & 11cafe Studio. 版权所有。
